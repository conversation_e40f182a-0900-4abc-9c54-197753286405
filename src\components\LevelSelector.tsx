'use client';

import { useState } from 'react';
import { Level } from '@/types';
import { useLanguage } from '@/contexts/LanguageContext';

interface LevelSelectorProps {
  onLevelSelect: (level: Level) => void;
  onAuthClick?: () => void;
  onHistoryClick?: () => void;
}

export default function LevelSelector({ onLevelSelect }: LevelSelectorProps) {
  const { t } = useLanguage();
  const [loadingLevel, setLoadingLevel] = useState<Level | null>(null);

  const levels = [
    {
      id: 'beginner' as Level,
      title: t('beginner'),
      description: t('beginnerDesc'),
      color: 'from-emerald-400 via-green-500 to-teal-600',
      hoverColor: 'from-emerald-500 via-green-600 to-teal-700',
      icon: '🌱',
      gradient: 'bg-gradient-to-br',
      accentColor: 'emerald'
    },
    {
      id: 'intermediate' as Level,
      title: t('intermediate'),
      description: t('intermediateDesc'),
      color: 'from-amber-400 via-orange-500 to-red-500',
      hoverColor: 'from-amber-500 via-orange-600 to-red-600',
      icon: '🌿',
      gradient: 'bg-gradient-to-br',
      accentColor: 'orange'
    },
    {
      id: 'advanced' as Level,
      title: t('advanced'),
      description: t('advancedDesc'),
      color: 'from-red-500 via-pink-500 to-purple-600',
      hoverColor: 'from-red-600 via-pink-600 to-purple-700',
      icon: '🌳',
      gradient: 'bg-gradient-to-br',
      accentColor: 'purple'
    }
  ];

  const handleLevelClick = async (level: Level) => {
    setLoadingLevel(level);
    // Add a small delay to show loading state
    await new Promise(resolve => setTimeout(resolve, 300));
    onLevelSelect(level);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-emerald-400/20 to-blue-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 pt-24 pb-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-500 rounded-3xl mb-6 shadow-2xl">
                <img
                  src="/logo.png"
                  alt="Infinite Language Logo"
                  className="w-16 h-16 object-contain"
                />
              </div>
            </div>
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-800 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-6 leading-tight">
              {t('appTitle')}
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-medium mb-6">
              {t('appSubtitle')}
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span>100% AI-Generated Content</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                <span>Unlimited Practice</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                <span>Personalized Learning</span>
              </div>
            </div>

            {/* Floating Stats */}
            <div className="mt-8 flex justify-center">
              <div className="bg-white/80 backdrop-blur-xl rounded-2xl px-8 py-4 shadow-xl border border-white/20">
                <div className="flex items-center space-x-8 text-sm">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">∞</div>
                    <div className="text-gray-600 font-medium">{t('questionsLabel')}</div>
                  </div>
                  <div className="w-px h-8 bg-gray-300"></div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-emerald-600">3</div>
                    <div className="text-gray-600 font-medium">{t('languagesLabel')}</div>
                  </div>
                  <div className="w-px h-8 bg-gray-300"></div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">🧠</div>
                    <div className="text-gray-600 font-medium">{t('aiPoweredLabel')}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Level Cards */}
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
            {levels.map((level) => {
              const isLoading = loadingLevel === level.id;
              return (
              <button
                key={level.id}
                onClick={() => handleLevelClick(level.id)}
                disabled={loadingLevel !== null}
                className={`group relative ${level.gradient} ${level.color} text-white p-6 lg:p-8 rounded-2xl shadow-xl transform transition-all duration-300 hover:scale-105 hover:shadow-2xl focus:outline-none focus:ring-4 focus:ring-white/50 disabled:opacity-75 disabled:cursor-not-allowed disabled:transform-none`}
              >
                {/* Content */}
                <div className="relative z-10">
                  <div className="text-4xl lg:text-5xl mb-4 transition-transform duration-300 group-hover:scale-110">
                    {level.icon}
                  </div>
                  <h3 className="text-2xl lg:text-3xl font-bold mb-3">
                    {level.title}
                  </h3>
                  <p className="text-sm lg:text-base opacity-90 leading-relaxed font-medium">
                    {level.description}
                  </p>

                  {/* Arrow indicator or loading spinner */}
                  <div className="mt-4 flex justify-center">
                    <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center transition-colors duration-300 group-hover:bg-white/30">
                      {isLoading ? (
                        <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                      ) : (
                        <svg className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                      )}
                    </div>
                  </div>
                </div>
              </button>
              );
            })}
          </div>

          {/* Quick Access Section */}
          <div className="mt-16">
            <div className="text-center mb-8">
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-4">
                Explore More Learning Options
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Beyond quizzes, discover comprehensive lessons and structured learning paths
              </p>
            </div>

            <div className="grid sm:grid-cols-1 lg:grid-cols-2 gap-6 mb-16 max-w-4xl mx-auto">
              <a
                href="/lessons"
                className="group bg-white/80 backdrop-blur-xl rounded-2xl p-8 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1"
              >
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-105 mx-auto">
                  <span className="text-4xl">🎓</span>
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3 text-center">{t('aiLessons')}</h3>
                <p className="text-gray-600 text-center">{t('lessonSubtitle')}</p>
              </a>

              <a
                href="/reading"
                className="group bg-white/80 backdrop-blur-xl rounded-2xl p-8 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1"
              >
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-105 mx-auto">
                  <span className="text-4xl">📰</span>
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3 text-center">{t('aiReading')}</h3>
                <p className="text-gray-600 text-center">{t('readingSubtitle')}</p>
              </a>
            </div>

            {/* AI Tutor Section */}
            <div className="mt-16">
              <div className="text-center mb-8">
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-4">
                  Get Personalized Help
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Ask our AI tutor any English question and get instant, detailed explanations
                </p>
              </div>

              <div className="max-w-2xl mx-auto">
                <a
                  href="/tutor"
                  className="group bg-gradient-to-r from-blue-500 via-purple-600 to-indigo-600 rounded-3xl p-8 shadow-2xl border border-white/20 hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 block"
                >
                  <div className="flex items-center justify-center mb-6">
                    <div className="w-20 h-20 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center transition-transform duration-300 group-hover:scale-110">
                      <span className="text-4xl">🤖</span>
                    </div>
                  </div>

                  <div className="text-center text-white">
                    <h3 className="text-2xl font-bold mb-3">AI English Tutor</h3>
                    <p className="text-blue-100 mb-4">
                      Get instant help with grammar, vocabulary, pronunciation, and more.
                      Ask questions in your native language and receive personalized explanations.
                    </p>

                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mb-6">
                      <div className="bg-white/10 backdrop-blur-xl rounded-lg p-3">
                        <div className="text-lg mb-1">📝</div>
                        <div className="text-xs">Grammar Help</div>
                      </div>
                      <div className="bg-white/10 backdrop-blur-xl rounded-lg p-3">
                        <div className="text-lg mb-1">📚</div>
                        <div className="text-xs">Vocabulary</div>
                      </div>
                      <div className="bg-white/10 backdrop-blur-xl rounded-lg p-3">
                        <div className="text-lg mb-1">🗣️</div>
                        <div className="text-xs">Pronunciation</div>
                      </div>
                      <div className="bg-white/10 backdrop-blur-xl rounded-lg p-3">
                        <div className="text-lg mb-1">💬</div>
                        <div className="text-xs">Usage Tips</div>
                      </div>
                      <div className="bg-white/10 backdrop-blur-xl rounded-lg p-3">
                        <div className="text-lg mb-1">🌍</div>
                        <div className="text-xs">Cultural Context</div>
                      </div>
                      <div className="bg-white/10 backdrop-blur-xl rounded-lg p-3">
                        <div className="text-lg mb-1">❓</div>
                        <div className="text-xs">Any Question</div>
                      </div>
                    </div>

                    <div className="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-xl rounded-lg text-white font-medium transition-all duration-300 group-hover:bg-white/30">
                      Start Chatting
                      <svg className="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </div>
                  </div>
                </a>
              </div>
            </div>


          </div>

          {/* Features Section */}
          <div className="mt-24">
            <div className="text-center mb-16">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4">
                {t('whyChooseTitle')}
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                {t('whyChooseSubtitle')}
              </p>
            </div>

            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="group bg-white/80 backdrop-blur-xl rounded-2xl p-8 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-105">
                  <span className="text-3xl">∞</span>
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">{t('aiGeneratedTitle')}</h3>
                <p className="text-gray-600 leading-relaxed">{t('aiGeneratedDesc')}</p>
              </div>

              <div className="group bg-white/80 backdrop-blur-xl rounded-2xl p-8 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-105">
                  <span className="text-3xl">🌐</span>
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">{t('multiLanguageTitle')}</h3>
                <p className="text-gray-600 leading-relaxed">{t('multiLanguageDesc')}</p>
              </div>

              <div className="group bg-white/80 backdrop-blur-xl rounded-2xl p-8 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 sm:col-span-2 lg:col-span-1">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-105">
                  <span className="text-3xl">📊</span>
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">{t('progressTrackingTitle')}</h3>
                <p className="text-gray-600 leading-relaxed">{t('progressTrackingDesc')}</p>
              </div>
            </div>

            {/* Learning Methods Section */}
            <div className="mt-16 bg-white/80 backdrop-blur-xl rounded-2xl p-8 shadow-xl border border-white/20">
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-6 text-center">
                Comprehensive English Learning Methods
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8 text-center">
                Our platform offers multiple learning approaches to suit different learning styles and preferences
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-gray-800">🎯 Interactive Quizzes</h3>
                  <p className="text-gray-600">
                    Test your knowledge with unlimited AI-generated questions across all skill levels.
                    Each question comes with detailed explanations to help you understand the concepts better.
                  </p>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Grammar and vocabulary questions</li>
                    <li>• Multiple choice and true/false formats</li>
                    <li>• Instant feedback and explanations</li>
                    <li>• Progress tracking and analytics</li>
                  </ul>
                </div>

                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-gray-800">📚 Structured Lessons</h3>
                  <p className="text-gray-600">
                    Learn through comprehensive lessons that cover theory, practice, and real-world applications.
                    Each lesson is tailored to your level and learning objectives.
                  </p>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Step-by-step learning progression</li>
                    <li>• Interactive exercises and examples</li>
                    <li>• Vocabulary building activities</li>
                    <li>• Cultural context and usage tips</li>
                  </ul>
                </div>

                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-gray-800">📖 Reading Comprehension</h3>
                  <p className="text-gray-600">
                    Improve your reading skills with engaging passages on various topics.
                    Develop critical thinking and vocabulary through contextual learning.
                  </p>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Diverse topics and text types</li>
                    <li>• Comprehension questions</li>
                    <li>• Vocabulary in context</li>
                    <li>• Cultural notes and insights</li>
                  </ul>
                </div>

                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-gray-800">🤖 AI Tutor Support</h3>
                  <p className="text-gray-600">
                    Get personalized help from our AI tutor available 24/7.
                    Ask questions in your native language and receive detailed explanations.
                  </p>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Grammar and usage explanations</li>
                    <li>• Pronunciation guidance</li>
                    <li>• Cultural context clarification</li>
                    <li>• Practice exercise suggestions</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Quick Start Section */}
            <div className="mt-16 text-center">
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-6">{t('start')} Learning Now</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
                Choose your preferred learning method and let AI create personalized content for you
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/lessons"
                  className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-colors font-medium text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                >
                  🎓 {t('createLesson')}
                </a>
                <a
                  href="/reading"
                  className="px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-xl hover:from-purple-600 hover:to-pink-700 transition-colors font-medium text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                >
                  📰 {t('generateReading')}
                </a>
              </div>
            </div>

            {/* SEO Content Section */}
            <div className="mt-16 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
                Why Choose AI-Powered English Learning?
              </h2>
              <div className="prose prose-gray max-w-4xl mx-auto">
                <p className="text-gray-700 mb-4">
                  Learning English has never been more accessible and effective than with our AI-powered platform.
                  Our innovative system generates unlimited, personalized content that adapts to your learning pace and style.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">🤖 AI-Powered Learning</h3>
                    <p className="text-gray-700">
                      Get instant, personalized feedback and content that matches your exact proficiency level.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">📚 Comprehensive Skills</h3>
                    <p className="text-gray-700">
                      Develop all four essential language skills through interactive quizzes, reading, and lessons.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">🌍 Cultural Context</h3>
                    <p className="text-gray-700">
                      Learn cultural context and real-world usage for effective communication.
                    </p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">⏰ Flexible Learning</h3>
                    <p className="text-gray-700">
                      Practice anytime, anywhere with content that adapts to your schedule.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
